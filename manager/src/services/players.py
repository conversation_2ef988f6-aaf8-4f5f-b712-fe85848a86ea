from datetime import datetime, time
from zoneinfo import ZoneInfo
from pydantic import BaseModel
from typing import List

from beanie import BulkWriter
from beanie.operators import Or, In, NotIn

from src.db.models import Player
from src.schemas.requests import (
    PlayerUpdateRequest,
    AddMultipleUserAccountsRequest,
    RegisterPlayerRequest,
)
from src.schemas.responses import (
    PlayerBalanceResponse,
    AddUserAccountResponse,
    PlayerResponseExtended,
    PlayerResponseFiltered,
)
from src.services.player_registration_service import PlayerRegistrationService
from src.utils.enums import AppType, BotType, PlayerStatus, CurrencyType
from src.services.ip_pool_service import IPPoolConfigurationService
from src.utils.logging import logger

USD_GOLD_CONVERSION_RATE = 8.0


class PlayerToUpdate(BaseModel):
    status: str | None = None
    player_id: str | None = None
    enabled: bool | None = None

    bot_id: str | None = None
    table_id: str | None = None
    should_stop: bool | None = None
    bot_type: str | None = None
    last_error: str | None = None
    balance: PlayerBalanceResponse | None = None
    need_balance_update: bool | None = None

    hands_played: int | None = None
    total_buy_in: int | None = None
    last_buy_in: int | None = None
    rebuy_count: int | None = None
    stack: float | None = None

    chips: float | None = None
    rank: int | None = None
    strategy_profile: str | None = None

    updated_at: datetime | None = None


class PlayerService:
    @staticmethod
    async def get_players(
        app_id: int = None,
        club_id: int = None,
        only_active: bool = False,
        only_enabled: bool = True,
    ) -> List[PlayerResponseExtended]:
        if app_id is None:
            db_players = Player.all()
        else:
            db_players = Player.find(
                Or(Player.app_id == app_id, In(Player.allowed_games, [app_id]))
            )
        if club_id:
            db_players = db_players.find(In(Player.club_ids, [club_id]))
        if only_enabled:
            db_players = db_players.find(Player.enabled == True)  # noqa: E712
        if only_active:
            db_players = db_players.find(Player.status != PlayerStatus.IDLE.value)  # noqa: E712

        players = await PlayerService.get_players_extended(await db_players.to_list())
        return players

    @staticmethod
    async def get_filtered_players(
        page: int,
        page_size: int,
        sort: str,
        query: List = [],
    ) -> PlayerResponseFiltered:
        # Query
        players = (
            await Player.find(*query)
            .sort(sort)
            .skip((page - 1) * page_size)
            .limit(page_size)
            .to_list()
        )
        total_count = await Player.find(*query).count()
        return PlayerResponseFiltered(
            total_count=total_count,
            page=page,
            page_size=page_size,
            players=await PlayerService.get_players_extended(players),
        )

    @staticmethod
    async def get_players_extended(players: List[Player]) -> List[PlayerResponseExtended]:
        result: List[PlayerResponseExtended] = []
        player_ids = [p.player_id for p in players]
        matched_confs = await IPPoolConfigurationService.get_multiple_configurations_for_players(
            player_ids
        )
        for player in players:
            proxy_conf = next(
                (conf for conf in matched_confs if player.player_id in conf.assigned_player_ids),
                None,
            )
            external_ip = proxy_conf.external_ip if proxy_conf else None
            ip_conf_id = proxy_conf.ip_conf_id if proxy_conf else None
            player_response = PlayerResponseExtended(
                **player.to_response().model_dump(), external_ip=external_ip, ip_conf_id=ip_conf_id
            )
            result.append(player_response)

        return result

    @staticmethod
    async def update_player(player_id: str, player_data: PlayerUpdateRequest) -> Player:
        player = await Player.find_one(Player.player_id == player_id)
        if not player:
            raise ValueError("Player not found")

        update_data = player_data.model_dump(exclude_none=True)

        await player.set(update_data)

        return player

    @staticmethod
    async def get_all_scan_players() -> List[Player]:
        return await Player.find(Player.bot_type == BotType.SCAN.value).to_list()

    @staticmethod
    async def update_players(players: List[PlayerToUpdate]):
        async with BulkWriter() as bulk_writer:
            for player in players:
                update_object = player.model_dump(exclude_unset=True)
                if update_object.get("updated_at") is None:
                    update_object["updated_at"] = datetime.now(ZoneInfo("UTC"))
                await Player.find_one(Player.player_id == player.player_id).set(update_object)

            await bulk_writer.commit()

    @staticmethod
    async def mark_player_for_balance_update(player_id: str):
        player = await Player.find_one(Player.player_id == player_id)
        player.need_balance_update = True
        await player.save()
        return player

    @staticmethod
    async def mark_players_for_balance_update(query: List) -> None:
        await Player.find(*query).set({"need_balance_update": True})

    @staticmethod
    async def add_user_accounts(
        request: AddMultipleUserAccountsRequest,
    ) -> List[AddUserAccountResponse]:
        user_account_responses: List[AddUserAccountResponse] = []

        for user_account in request.accounts:
            register_player_request = RegisterPlayerRequest(
                username=user_account.username,
                account=user_account.account,
                password=user_account.password,
                country_code=user_account.country_code,
                area_code=user_account.area_code,
                phone_number=user_account.phone_number,
                app_id=request.app_id,
                platform_id=request.platform_id,
            )
            register_player_response = await PlayerRegistrationService().register_player(
                request=register_player_request,
                is_register_request=False,
            )
            user_account_response = AddUserAccountResponse(
                player_id=register_player_response.player_id,
                platform_id=register_player_response.platform_id,
                account=register_player_response.account,
                external_ip=register_player_response.external_ip,
                error=register_player_response.error,
                exception=register_player_response.exception,
                country_code=user_account.country_code,
                phone_number=user_account.phone_number,
            )

            user_account_responses.append(user_account_response)

        return user_account_responses

    @staticmethod
    def convert_currency(
        amount: float | int, from_currency: CurrencyType, to_currency: CurrencyType
    ):
        if from_currency == to_currency:
            return amount

        if from_currency == CurrencyType.USD and to_currency == CurrencyType.GOLD:
            return amount * USD_GOLD_CONVERSION_RATE
        if from_currency == CurrencyType.GOLD and to_currency == CurrencyType.USD:
            return amount / USD_GOLD_CONVERSION_RATE

        raise ValueError(f"You probably shouldn't convert from {from_currency} to {to_currency}")

    @staticmethod
    def build_allowed_players_query(app_id: AppType, play_time: time) -> list:
        return [
            Player.status == PlayerStatus.IDLE.value,
            Player.enabled == True,  # noqa: E712
            Or(  # hacky subquery to filter some players to check by play_time_range, because Pymongo does not support "$expr" to compare fields internally
                Player.play_time_range.start < play_time,
                Player.play_time_range.end > play_time,
            ),
            Or(
                In(Player.allowed_games, [app_id]),
                Player.app_id == app_id,
            ),
        ]

    @staticmethod
    def build_players_for_game_query(
        query: List, app_id: AppType, play_time: time, currency: CurrencyType, fee: float, allowed_ticket_ids: list[int] = None
    ) -> list:

        # Filter by registration fee and currency
        match currency:
            case CurrencyType.USD:
                gold_fee = PlayerService.convert_currency(fee, CurrencyType.USD, CurrencyType.GOLD)
                query.append(
                    Or(
                        Player.balance.gold >= gold_fee,
                        Player.balance.usd >= fee,
                    )
                )
            case CurrencyType.GOLD:
                usd_fee = PlayerService.convert_currency(fee, CurrencyType.GOLD, CurrencyType.USD)
                query.append(
                    Or(
                        Player.balance.gold >= fee,
                        Player.balance.usd >= usd_fee,
                    )
                )
            case CurrencyType.DIAMOND:
                query.append(Player.balance.diamond >= fee)
            case _:
                raise ValueError(f"Unsupported currency: {currency}. Expected one of ['USD', 'GOLD', 'DIAMOND']")

        return query

    @staticmethod
    async def find_players_for_tournament(
        tournament_app_id: int,
        play_time: time,
        currency: CurrencyType,
        fee: float,
        sign_up_options: str = None,
        exclude_player_ids: list[str] = None,
    ) -> tuple[list[Player], list[Player]]:
        """
        Find players for tournament, prioritizing those with matching tickets.
        Returns (players_with_tickets, players_with_balance) where players_with_tickets
        have matching tickets and players_with_balance meet balance requirements.
        """

        query = PlayerService.build_allowed_players_query(tournament_app_id, play_time)

        allowed_ticket_ids = PlayerService.extract_ticket_ids(sign_up_options)
        players_for_game_query = PlayerService.build_players_for_game_query(
            query, tournament_app_id, play_time, currency, fee, allowed_ticket_ids
        )


        # players_with_tickets = []
        # if allowed_ticket_ids:
        #     players_with_tickets = await PlayerService._find_players_with_tickets(
        #         tournament_app_id, play_time, allowed_ticket_ids, exclude_player_ids
        #     )

        # Get players with sufficient balance (excluding those with tickets)
        # players_with_balance = await PlayerService._find_players_with_balance(
        #     tournament_app_id, play_time, currency, fee, exclude_player_ids, players_with_tickets
        # )

        players = await Player.find(*players_for_game_query).to_list()

        # logger.debug(
        #     "PlayerService.find_players_for_tournament",
        #     f"Found {len(players_with_tickets)} players with matching tickets "
        #     f"and {len(players_with_balance)} players with sufficient balance for tournament tickets {allowed_ticket_ids}",
        # )

        return players

    @staticmethod
    async def _find_players_with_tickets(
        tournament_app_id: int,
        play_time: time,
        allowed_ticket_ids: list[int],
        exclude_player_ids: list[str] = None,
    ) -> list[Player]:
        """
        Find players with matching tickets using optimized MongoDB query.
        """
        ticket_query = PlayerService.build_allowed_players_query(tournament_app_id, play_time)
        ticket_query.append(In(Player.balance.tickets, allowed_ticket_ids))

        if exclude_player_ids:
            ticket_query.append(NotIn(Player.player_id, exclude_player_ids))

        players = await Player.find(*ticket_query).to_list()
        return [p for p in players if p.is_in_play_time_range(play_time)]

    @staticmethod
    async def _find_players_with_balance(
        tournament_app_id: int,
        play_time: time,
        currency: CurrencyType,
        fee: float,
        exclude_player_ids: list[str] = None,
        players_with_tickets: list[Player] = None,
    ) -> list[Player]:
        """
        Find players with sufficient balance using optimized MongoDB query.
        Excludes players who already have matching tickets.
        """
        query = PlayerService.build_allowed_players_query(tournament_app_id, play_time)

        balance_query = PlayerService.build_players_for_game_query(
            query, tournament_app_id, play_time, currency, fee
        )

        players = await Player.find(*balance_query).to_list()
        return [p for p in players if p.is_in_play_time_range(play_time)]

    @staticmethod
    def extract_ticket_ids(sign_up_options: str) -> list[int]:
        """
        Extract ticket IDs from signUpOptions comma-separated string.
        Example: "gold,tool,specific:mtt:a92:1754,a92:1593" -> [1754, 1593]
        """
        if not sign_up_options:
            return []

        import re

        ticket_ids = []
        # Find all numbers that come after a colon
        matches = re.findall(r":(\d+)", sign_up_options)

        for match in matches:
            try:
                ticket_ids.append(int(match))
            except ValueError:
                continue

        return ticket_ids

    @staticmethod
    def find_matching_ticket_id(player_tickets: list, allowed_ticket_ids: list[int]) -> int | None:
        if not player_tickets or not allowed_ticket_ids:
            return None

        # Convert allowed_ticket_ids to a set for faster lookup
        allowed_set = set(allowed_ticket_ids)

        for ticket in player_tickets:
            if isinstance(ticket, int) and ticket in allowed_set:
                return ticket
            elif isinstance(ticket, str):
                try:
                    ticket_int = int(ticket)
                    if ticket_int in allowed_set:
                        return ticket_int
                except ValueError:
                    continue

        return None

    @staticmethod
    async def update_avatar(player_id: str, avatar_url: str) -> Player:
        player = await Player.find_one(Player.player_id == player_id)
        player.avatar_url = avatar_url
        player.avatar_changed = True
        await player.save()
        return player
